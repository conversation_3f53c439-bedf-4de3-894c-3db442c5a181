# Install required packages
!pip install google-generativeai pillow opencv-python matplotlib pandas numpy

# Import necessary libraries
import json
import os
import base64
import io
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import google.generativeai as genai
from google.colab import files, userdata
import warnings
warnings.filterwarnings('ignore')

# Configure Gemini AI
# You need to add your Gemini API key to Colab secrets with name 'GEMINI_API_KEY'
try:
    GEMINI_API_KEY = userdata.get('GEMINI_API_KEY')
    genai.configure(api_key=GEMINI_API_KEY)
    print("✅ Gemini AI configured successfully")
except Exception as e:
    print("❌ Please add your Gemini API key to Colab secrets with name 'GEMINI_API_KEY'")
    print("Go to the key icon on the left sidebar and add your API key")
    GEMINI_API_KEY = input("Or enter your Gemini API key here: ")
    genai.configure(api_key=GEMINI_API_KEY)

# Initialize the model
model = genai.GenerativeModel('gemini-1.5-flash')
print("✅ Gemini model initialized")

# Upload required files
print("📁 Please upload the following files:")
print("1. Screenshot image (PNG/JPG)")
print("2. Bounding box JSON file")
print("3. Labels text file")

uploaded = files.upload()

# Identify uploaded files
screenshot_file = None
bounding_box_file = None
labels_file = None

for filename in uploaded.keys():
    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
        screenshot_file = filename
        print(f"🖼️ Screenshot: {filename}")
    elif filename.lower().endswith('.json'):
        bounding_box_file = filename
        print(f"📊 Bounding boxes: {filename}")
    elif filename.lower().endswith('.txt'):
        labels_file = filename
        print(f"🏷️ Labels: {filename}")

# Validate all files are uploaded
if not all([screenshot_file, bounding_box_file, labels_file]):
    print("❌ Missing required files. Please upload:")
    if not screenshot_file: print("  - Screenshot image")
    if not bounding_box_file: print("  - Bounding box JSON")
    if not labels_file: print("  - Labels text file")
else:
    print("✅ All required files uploaded successfully!")

# Load bounding box data
with open(bounding_box_file, 'r') as f:
    bounding_boxes = json.load(f)

print(f"📊 Loaded {len(bounding_boxes)} bounding boxes")

# Load screenshot
screenshot = Image.open(screenshot_file)
print(f"🖼️ Screenshot dimensions: {screenshot.size}")

# Load available labels
with open(labels_file, 'r') as f:
    available_labels = [line.strip() for line in f.readlines() if line.strip()]

print(f"🏷️ Available labels: {len(available_labels)}")
print("First 10 labels:", available_labels[:10])

# Filter meaningful UI elements (remove very small or very large elements)
def filter_meaningful_elements(bounding_boxes, min_area=100, max_area_ratio=0.8):
    """
    Filter bounding boxes to keep only meaningful UI elements
    """
    screenshot_area = screenshot.size[0] * screenshot.size[1]
    max_area = screenshot_area * max_area_ratio
    
    filtered_elements = []
    
    for element in bounding_boxes:
        area = element['width'] * element['height']
        
        # Filter criteria
        if (area >= min_area and 
            area <= max_area and 
            element['width'] > 10 and 
            element['height'] > 10 and
            element['tag'] not in ['html', 'body']):
            
            filtered_elements.append(element)
    
    return filtered_elements

# Apply filtering
filtered_elements = filter_meaningful_elements(bounding_boxes)
print(f"🔍 Filtered to {len(filtered_elements)} meaningful UI elements")

# Sort by area (largest first) and take top 50 for processing
filtered_elements.sort(key=lambda x: x['width'] * x['height'], reverse=True)
elements_to_process = filtered_elements[:50]
print(f"📋 Processing top {len(elements_to_process)} elements")

def extract_element_image(screenshot, element, padding=5):
    """
    Extract a close-up image of a UI element from the screenshot
    """
    x, y, width, height = element['x'], element['y'], element['width'], element['height']
    
    # Add padding and ensure bounds
    x1 = max(0, x - padding)
    y1 = max(0, y - padding)
    x2 = min(screenshot.size[0], x + width + padding)
    y2 = min(screenshot.size[1], y + height + padding)
    
    # Extract the region
    element_image = screenshot.crop((x1, y1, x2, y2))
    
    return element_image

def create_annotated_screenshot(screenshot, elements, max_elements=20):
    """
    Create an annotated screenshot showing bounding boxes
    """
    annotated = screenshot.copy()
    draw = ImageDraw.Draw(annotated)
    
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'yellow', 'cyan', 'magenta']
    
    for i, element in enumerate(elements[:max_elements]):
        x, y, width, height = element['x'], element['y'], element['width'], element['height']
        color = colors[i % len(colors)]
        
        # Draw bounding box
        draw.rectangle([x, y, x + width, y + height], outline=color, width=2)
        
        # Draw element number
        draw.text((x, y-15), str(i+1), fill=color)
    
    return annotated

# Create annotated screenshot
annotated_screenshot = create_annotated_screenshot(screenshot, elements_to_process)
plt.figure(figsize=(15, 10))
plt.imshow(annotated_screenshot)
plt.title("Annotated Screenshot with UI Elements")
plt.axis('off')
plt.show()

print("🎯 Annotated screenshot created showing top UI elements")

def prepare_element_data_for_gemini(element):
    """
    Prepare element metadata for Gemini analysis
    """
    metadata = {
        'coordinates': f"x:{element['x']}, y:{element['y']}, width:{element['width']}, height:{element['height']}",
        'tag': element['tag'],
        'class': element.get('class', ''),
        'id': element.get('id', ''),
        'selector': element.get('selector', ''),
        'area': element['width'] * element['height']
    }
    return metadata

def create_annotated_full_screenshot(screenshot, elements_batch):
    """
    Create an annotated full screenshot highlighting the current batch of elements
    """
    annotated = screenshot.copy()
    draw = ImageDraw.Draw(annotated)
    
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    
    for i, element in enumerate(elements_batch):
        x, y, width, height = element['x'], element['y'], element['width'], element['height']
        color = colors[i % len(colors)]
        
        # Draw bounding box with thicker border
        draw.rectangle([x, y, x + width, y + height], outline=color, width=3)
        
        # Draw element number with background
        text = str(i + 1)
        try:
            # Try to get text dimensions (newer Pillow versions)
            bbox = draw.textbbox((0, 0), text)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
        except AttributeError:
            # Fallback for older Pillow versions
            text_width, text_height = draw.textsize(text)
        
        # Background rectangle for number
        draw.rectangle([x, y-25, x + text_width + 10, y], fill=color)
        draw.text((x + 5, y-20), text, fill='white')
    
    return annotated

def analyze_elements_batch_with_gemini(screenshot, elements_batch, available_labels):
    """
    Use Gemini AI to analyze a batch of up to 5 UI elements at once
    """
    # Convert full screenshot to bytes
    full_img_byte_arr = io.BytesIO()
    annotated_screenshot = create_annotated_full_screenshot(screenshot, elements_batch)
    annotated_screenshot.save(full_img_byte_arr, format='PNG')
    full_img_byte_arr = full_img_byte_arr.getvalue()
    
    # Prepare images and metadata for the batch
    images_data = []
    elements_info = []
    
    for i, element in enumerate(elements_batch):
        # Extract close-up image
        element_image = extract_element_image(screenshot, element)
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        element_image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        images_data.append({
            "mime_type": "image/png",
            "data": img_byte_arr
        })
        
        # Prepare metadata
        metadata = prepare_element_data_for_gemini(element)
        elements_info.append({
            'element_number': i + 1,
            'coordinates': metadata['coordinates'],
            'tag': metadata['tag'],
            'class': metadata['class'],
            'id': metadata['id'],
            'selector': metadata['selector'],
            'area': metadata['area']
        })
    
    # Create comprehensive prompt for batch processing
    elements_details = "\n".join([
        f"Element {info['element_number']}:\n"
        f"  - Coordinates: {info['coordinates']}\n"
        f"  - HTML Tag: {info['tag']}\n"
        f"  - CSS Class: {info['class']}\n"
        f"  - ID: {info['id']}\n"
        f"  - CSS Selector: {info['selector']}\n"
        f"  - Area: {info['area']} pixels\n"
        for info in elements_info
    ])
    
    prompt = f"""
    Analyze the UI elements in the provided images and assign the most appropriate labels from the available list.
    
    You will see:
    1. First image: Full screenshot with numbered bounding boxes (1-{len(elements_batch)}) highlighting the elements to analyze
    2. Next {len(elements_batch)} images: Close-up views of each numbered element
    
    Elements Details:
    {elements_details}
    
    Available Labels:
    {', '.join(available_labels)}
    
    Instructions:
    1. Analyze both the full screenshot context and the close-up images
    2. Consider the HTML metadata (tag, class, ID) for each element
    3. Choose the MOST SPECIFIC and APPROPRIATE label for each element
    4. Consider the element's visual appearance, position, and likely function
    5. Use the full screenshot to understand the element's context within the page
    
    Respond with EXACTLY this format:
    Element 1: [chosen_label]
    Element 2: [chosen_label]
    Element 3: [chosen_label]
    Element 4: [chosen_label]
    Element 5: [chosen_label]
    
    If fewer than 5 elements, only respond for the elements provided.
    If no suitable label exists for an element, use "Unknown".
    """
    
    try:
        # Prepare content for Gemini (full screenshot + close-up images)
        content = [prompt]
        
        # Add full annotated screenshot first
        content.append({
            "mime_type": "image/png",
            "data": full_img_byte_arr
        })
        
        # Add close-up images
        content.extend(images_data)
        
        # Get response from Gemini
        response = model.generate_content(content)
        response_text = response.text.strip()
        
        # Parse the response
        predicted_labels = []
        lines = response_text.split('\n')
        
        for line in lines:
            if ':' in line and line.strip().startswith('Element'):
                label = line.split(':', 1)[1].strip()
                
                # Validate label
                if label in available_labels:
                    predicted_labels.append(label)
                else:
                    # Try to find a close match
                    found_match = False
                    for available_label in available_labels:
                        if (label.lower() in available_label.lower() or 
                            available_label.lower() in label.lower()):
                            predicted_labels.append(available_label)
                            found_match = True
                            break
                    if not found_match:
                        predicted_labels.append("Unknown")
        
        # Ensure we have labels for all elements
        while len(predicted_labels) < len(elements_batch):
            predicted_labels.append("Unknown")
            
        return predicted_labels[:len(elements_batch)]
        
    except Exception as e:
        print(f"Error analyzing batch: {e}")
        return ["Error"] * len(elements_batch)

print("🤖 Enhanced Gemini batch analysis functions ready")

# Process elements in batches of 5 for Gemini analysis
results = []
batch_size = 5  # Process 5 elements at a time for optimal Gemini performance

print("🔄 Starting enhanced batch processing of UI elements...")
print(f"Processing {len(elements_to_process)} elements in batches of {batch_size}")
print("Each batch includes: Full screenshot + Close-up images + Metadata")

for i in range(0, len(elements_to_process), batch_size):
    batch = elements_to_process[i:i+batch_size]
    batch_num = i//batch_size + 1
    total_batches = (len(elements_to_process)-1)//batch_size + 1
    
    print(f"\n📦 Processing batch {batch_num}/{total_batches} ({len(batch)} elements)")
    print(f"  Elements {i+1}-{i+len(batch)} of {len(elements_to_process)}")
    
    # Analyze the entire batch with Gemini
    print(f"  🤖 Sending batch to Gemini AI...")
    predicted_labels = analyze_elements_batch_with_gemini(screenshot, batch, available_labels)
    
    # Process results for each element in the batch
    for j, (element, predicted_label) in enumerate(zip(batch, predicted_labels)):
        element_idx = i + j + 1
        
        # Extract element image for storage
        element_image = extract_element_image(screenshot, element)
        
        # Prepare metadata
        metadata = prepare_element_data_for_gemini(element)
        
        # Store results
        result = {
            'element_id': element_idx,
            'coordinates': metadata['coordinates'],
            'tag': metadata['tag'],
            'class': metadata['class'],
            'id': metadata['id'],
            'selector': metadata['selector'],
            'area': metadata['area'],
            'predicted_label': predicted_label,
            'element_image': element_image
        }
        
        results.append(result)
        print(f"    ✅ Element {element_idx}: {predicted_label}")
    
    # Delay between batches to respect API rate limits
    if i + batch_size < len(elements_to_process):
        import time
        print(f"  ⏳ Waiting 2 seconds before next batch...")
        time.sleep(2)

print(f"\n🎉 Completed enhanced batch processing of {len(results)} elements!")
print(f"📊 Used {total_batches} API calls instead of {len(results)} (saved {len(results) - total_batches} calls)")

# Create DataFrame for analysis
df_results = pd.DataFrame([{
    'element_id': r['element_id'],
    'coordinates': r['coordinates'],
    'tag': r['tag'],
    'class': r['class'][:50] + '...' if len(r['class']) > 50 else r['class'],  # Truncate long class names
    'id': r['id'],
    'area': r['area'],
    'predicted_label': r['predicted_label']
} for r in results])

print("📊 Results Summary:")
print(f"Total elements processed: {len(df_results)}")
print(f"Unique labels assigned: {df_results['predicted_label'].nunique()}")
print(f"Most common labels:")
print(df_results['predicted_label'].value_counts().head(10))

# Display results table
print("\n📋 Detailed Results:")
display(df_results.head(20))

# Visualize sample results with images
def display_sample_results(results, num_samples=12):
    """
    Display a grid of sample UI elements with their predicted labels
    """
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.flatten()
    
    sample_results = results[:num_samples]
    
    for i, result in enumerate(sample_results):
        if i >= len(axes):
            break
            
        ax = axes[i]
        ax.imshow(result['element_image'])
        ax.set_title(f"#{result['element_id']}: {result['predicted_label']}\n{result['tag']}", 
                    fontsize=10, pad=10)
        ax.axis('off')
    
    # Hide unused subplots
    for i in range(len(sample_results), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.suptitle("Sample UI Elements with Predicted Labels", fontsize=16, y=1.02)
    plt.show()

display_sample_results(results)
print("🖼️ Sample results visualization complete")

# Export results to various formats
import datetime

timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# 1. Export to CSV
csv_filename = f"ui_labeling_results_{timestamp}.csv"
df_results.to_csv(csv_filename, index=False)
print(f"📄 Results exported to CSV: {csv_filename}")

# 2. Export to JSON with full metadata
json_results = []
for result in results:
    json_result = {
        'element_id': result['element_id'],
        'coordinates': result['coordinates'],
        'tag': result['tag'],
        'class': result['class'],
        'id': result['id'],
        'selector': result['selector'],
        'area': result['area'],
        'predicted_label': result['predicted_label']
    }
    json_results.append(json_result)

json_filename = f"ui_labeling_results_{timestamp}.json"
with open(json_filename, 'w') as f:
    json.dump(json_results, f, indent=2)
print(f"📄 Results exported to JSON: {json_filename}")

# 3. Create summary report
summary_filename = f"ui_labeling_summary_{timestamp}.txt"
with open(summary_filename, 'w') as f:
    f.write(f"UI Element Labeling Summary Report\n")
    f.write(f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    f.write(f"Total elements processed: {len(results)}\n")
    f.write(f"Unique labels assigned: {df_results['predicted_label'].nunique()}\n\n")
    f.write("Label distribution:\n")
    for label, count in df_results['predicted_label'].value_counts().items():
        f.write(f"  {label}: {count}\n")
    f.write("\nTop elements by area:\n")
    top_elements = df_results.nlargest(10, 'area')
    for _, row in top_elements.iterrows():
        f.write(f"  Element #{row['element_id']}: {row['predicted_label']} ({row['area']} px²)\n")

print(f"📄 Summary report exported: {summary_filename}")

# 4. Download files
print("\n📥 Downloading files...")
files.download(csv_filename)
files.download(json_filename)
files.download(summary_filename)

print("\n✅ All files exported and downloaded successfully!")

# Advanced analysis and insights
print("🔍 Advanced Analysis:")

# 1. Label distribution by HTML tag
print("\n📊 Label distribution by HTML tag:")
tag_label_crosstab = pd.crosstab(df_results['tag'], df_results['predicted_label'])
print(tag_label_crosstab)

# 2. Element size analysis
print("\n📏 Element size analysis:")
size_stats = df_results.groupby('predicted_label')['area'].agg(['count', 'mean', 'median', 'std']).round(2)
print(size_stats.head(10))

# 3. Create visualization of label distribution
plt.figure(figsize=(12, 8))
label_counts = df_results['predicted_label'].value_counts().head(15)
plt.barh(range(len(label_counts)), label_counts.values)
plt.yticks(range(len(label_counts)), label_counts.index)
plt.xlabel('Count')
plt.title('Top 15 Most Common UI Element Labels')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

# 4. Confidence analysis (elements labeled as "Unknown" or "Error")
uncertain_labels = df_results[df_results['predicted_label'].isin(['Unknown', 'Error'])]
print(f"\n❓ Elements with uncertain labels: {len(uncertain_labels)} ({len(uncertain_labels)/len(df_results)*100:.1f}%)")

if len(uncertain_labels) > 0:
    print("Uncertain elements by tag:")
    print(uncertain_labels['tag'].value_counts())

print("\n🎯 Analysis complete!")